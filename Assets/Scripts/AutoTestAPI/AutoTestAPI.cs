using UnityEngine;
using WebSocketSharp;
using System;
using Cysharp.Threading.Tasks;
using Newtonsoft.Json;

[Serializable]
public class Command
{
    public string action;
}

public class AutoTestAPI : MonoBehaviour
{
    private WebSocket ws;

    [Serializable]
    public struct StateData
    {
        public GameStateTracker.GameState state;
    }
    
    private void Start()
    {
        ws = new WebSocket("ws://localhost:8765/");
        ws.OnMessage += (sender, e) =>
        {
            Debug.Log("Received: " + e.Data);
        };
        ws.Connect();
        
        Send().Forget();
    }

    private async UniTaskVoid Send()
    {
        while (true)
        {
            await UniTask.WaitForSeconds(1);
            var state = new StateData { state = GameStateTracker.Instance.CurrentState };
            ws.Send(JsonConvert.SerializeObject(state));
        }
    }
}