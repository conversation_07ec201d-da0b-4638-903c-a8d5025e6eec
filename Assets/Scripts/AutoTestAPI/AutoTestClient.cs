using UnityEngine;
using WebSocketSharp;
using System;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using Newtonsoft.Json;

namespace AutoTestAPI
{
    [Serializable]
    public class Command
    {
        public string action;
    }

    interface ICommandHandler
    {
        public List<string> GetCommands();
    }

    public class AutoTestClient : MonoBehaviour
    {
        [SerializeField] private List<ICommandHandler> _commandHandlers = new();
        
        private WebSocket ws;
        private Dictionary<string, ICommandHandler> _handlersByCommands = new();
        
        [Serializable]
        public struct StateData
        {
            public GameStateTracker.GameState state;
        }

        private void Start()
        {
            GetComponents(_commandHandlers);

            foreach (var commandHandler in _commandHandlers)
            {
                foreach (var command in commandHandler.GetCommands())
                {
                    _handlersByCommands.Add(command, commandHandler);
                }
            }
            
            ws = new WebSocket("ws://localhost:8765/");
            ws.OnMessage += (sender, e) => { Debug.Log("Received: " + e.Data); };
            ws.Connect();

            Send().Forget();
        }

        private async UniTaskVoid Send()
        {
            while (true)
            {
                await UniTask.WaitForSeconds(1);
                var state = new StateData { state = GameStateTracker.Instance.CurrentState };
                ws.Send(JsonConvert.SerializeObject(state));
            }
        }
    }
}