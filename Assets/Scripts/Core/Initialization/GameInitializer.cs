#define LOG_GameInitializer
// #define LOG_WARNING_GameInitializer
// #define LOG_ERROR_GameInitializer

using System;
using System.Collections.Generic;
using Consts;
using Cysharp.Threading.Tasks;
using Sirenix.OdinInspector;
using Unity.Netcode;
using Unity.Netcode.Transports.UTP;
using Unity.Services.Authentication;
using UnityEngine;
using UnityEngine.SceneManagement;
using Utils.PrefabManagement;
using Exception = System.Exception;

#if UNITY_EDITOR
using ParrelSync;
#endif

namespace Utils.Core
{
    public partial class GameInitializer : SerializedMonoBehaviour
    {
        public static GameInitializer Instance { get; private set; }

        [field: SerializeField] public GameObject autoTestApiPrefab { get; private set; }

        #region Scriptables

        private const string ScriptablesFoldoutGroup = "Scriptables References";

        //[SerializeField, FoldoutGroup(ScriptablesFoldoutGroup)]
        //private MetaDictionary metaDictionary;

        [SerializeField, FoldoutGroup(ScriptablesFoldoutGroup)]
        private List<IBindableScriptableObject> bindableScriptables;

        private List<ITickable> _tickables = new();
        private List<IFixedTickable> _fixedTickables = new();

        #endregion

        private void Awake()
        {
            if (Instance == null)
                Instance = this;

            QualitySettings.vSyncCount = 0;
            Application.targetFrameRate = 60;

            GameInitializationCoroutine().Forget();
        }

        private async UniTaskVoid GameInitializationCoroutine()
        {
            InitializeScriptables();

            while (!Rewired.ReInput.isReady)
            {
                await UniTask.WaitForFixedUpdate();
            }

            InitializeManagers();

            while (!MultiplayerManager.IsInitialized)
            {
                await UniTask.WaitForFixedUpdate();
            }

            ProcessDebugSettings();
            
            if (Args.Instance.AutoTestMode)
                Instantiate(autoTestApiPrefab);
        }

        private static void ProcessDebugSettings()
        {
            var debugSettings = GameConfig.Instance.DebugSettings;
            switch (debugSettings.LaunchMode)
            {
                case LaunchMode.ParrelSyncHostAndJoin:
#if UNITY_EDITOR
                    if (ClonesManager.IsClone())
                    {
                        DebugJoinLobby().Forget();
                    }
                    else
                    {
                        DebugStartLobby().Forget();
                    }
#endif

                    break;
                case LaunchMode.FastSoloStart:
                    NetworkManager.Singleton.GetComponent<UnityTransport>().SetConnectionData("127.0.0.1", 7777);
                    MultiplayerManager.Instance.StartHost();

                    MultiplayerManager.Instance.StartGameAsServer(true, debugSettings.StartInElevator, debugSettings.TravelImmediately);
                    break;
                default:
                    SceneManager.LoadScene(StringConsts.MainMenu, LoadSceneMode.Additive);
                    break;
            }
        }

        private static async UniTaskVoid DebugStartLobby()
        {
#if UNITY_EDITOR
            while (!AuthenticationService.Instance.IsSignedIn)
            {
                await UniTask.WaitForFixedUpdate();
            }

            await LobbyManager.Instance.CreateLobby("Debug Lobby", true);
            LobbyManager.Instance.StoreLobbyCode();
            await MultiplayerManager.Instance.LoadSceneWhenReady(StringConsts.CharacterSelection);
            MultiplayerManager.Instance.ToggleReady();


            while (NetworkManager.Singleton.ConnectedClients.Count < CloneController.UnityProcesses.Count)
            {
                await UniTask.WaitForFixedUpdate();
            }

            MultiplayerManager.Instance.StartGameAsServer();
            LobbyManager.Instance.DeleteStoredLobbyCode();
#endif
        }

        private static async UniTaskVoid DebugJoinLobby()
        {
#if UNITY_EDITOR
            Log("Waiting for authentication");
            while (!AuthenticationService.Instance.IsSignedIn)
            {
                await UniTask.WaitForFixedUpdate();
            }

            Log("Waiting for lobby to start...");

            bool connected = false;
            do
            {
                try
                {
                    var lobbyCode = LobbyManager.Instance.GetStoredLobbyCode();
                    if (!string.IsNullOrEmpty(lobbyCode))
                    {
                        Log($"Trying to join lobby with code {lobbyCode}");
                        connected = await LobbyManager.Instance.JoinWithCode(lobbyCode);
                        Log($"Joining result: {connected}");
                    }
                }
                catch (Exception e)
                {
                    LogError(e.Message);
                }

                if (!connected)
                    await UniTask.WaitForSeconds(2f);
            } while (!connected);

            while (!NetworkManager.Singleton.IsConnectedClient)
            {
                await UniTask.WaitForFixedUpdate();
            }

            MultiplayerManager.Instance.ToggleReady();
#endif
        }


        private void InitializeScriptables()
        {
            foreach (var bindableScriptable in bindableScriptables)
                bindableScriptable.Bind();
        }

        private void InitializeManagers()
        {
            PhysicsCollisionMatrixLayerMasks.Init();

            DontDestroyOnLoad(new GameObject(nameof(CoroutinesHost), typeof(CoroutinesHost)));

            var prefabSpawner = PrefabSpawner.CreateInstance();
            var lobbyManager = LobbyManager.CreateInstance();
            var settingsManager = SettingsManager.CreateInstance();
            var pauseManager = InGameMenuOpener.CreateInstance();
            var uiManager = UIManager.CreateInstance();

            settingsManager.Initialize();
        }

        private void Update()
        {
            foreach (var tickable in _tickables)
                tickable.Update();
        }

        private void FixedUpdate()
        {
            //foreach (var fixedTickable in _fixedTickables.AsSpan())
            //    fixedTickable.FixedUpdate(Time.fixedDeltaTime);
        }

        public void RegisterTickable(object tickableCandidate)
        {
            if (tickableCandidate is ITickable tickable)
                _tickables.Add(tickable);

            if (tickableCandidate is IFixedTickable fixedTickable)
                _fixedTickables.Add(fixedTickable);
        }

        public void ForgetTickable(object tickableCandidate)
        {
            if (tickableCandidate is ITickable tickable)
                _tickables.Remove(tickable);

            if (tickableCandidate is IFixedTickable fixedTickable)
                _fixedTickables.Remove(fixedTickable);
        }
    }
}