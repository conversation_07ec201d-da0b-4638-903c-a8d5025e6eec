using System;
using UnityEngine;
using Mono.Options;

public class Args : MonoBeh<PERSON>our
{
    public static Args Instance { get; private set; }

    public bool AutoTestMode { get; private set; }
    public int Port { get; private set; } = 8765;

    private void Awake()
    {
        if (Instance != null)
        {
            Destroy(gameObject);
        }

        Instance = this;

        var options = new OptionSet
        {
            {
                "autotest", "Activate autotest client and connect to an orchtrator",
                _ => AutoTestMode = true
            },
            {
                "port=", "the port to connect on",
                (int v) => Port = v
            }
        };

        var args = Environment.GetCommandLineArgs();
        options.Parse(args);
    }
}