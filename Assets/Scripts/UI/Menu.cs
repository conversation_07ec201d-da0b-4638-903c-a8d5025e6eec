using System;
using System.Collections;
using System.Collections.Generic;
using Rewired;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

public class Menu : MonoBehaviour, IWindowOpenedListner
{
    [SerializeField] private GameObject defaultSelectedItem;
    [SerializeField] private bool closeWindowOnBack;
    [SerializeField] private bool isRootMenu;
    [SerializeField] private List<RectTransform> rebuildList;

    public static Menu ActiveMenu { get; private set; }
    
    public Window window { get; set; }
    public Menu PreviousMenu { get; set; }

    protected Player rewiredPlayer;
    private GameObject _lastSelectedGameObject;

    private List<Selectable> _menuItems = new();

    protected Player RewiredPlayer => rewiredPlayer ??= ReInput.players.GetPlayer(0);

    protected virtual void Awake()
    {
        GetComponentsInChildren(_menuItems);

        if (defaultSelectedItem == null && _menuItems.Count > 0)
            defaultSelectedItem = _menuItems[0].gameObject;
        
        for (var i = 0; i < _menuItems.Count; i++)
        {
            var menuItem = _menuItems[i];

            var navigation = menuItem.navigation;

            if (!menuItem.interactable)
            {
                navigation.mode = Navigation.Mode.None;
            }
            else
            {
                navigation.mode = Navigation.Mode.Explicit;
                navigation.selectOnDown = GetNextInteractable(i);
                navigation.selectOnUp = GetNextInteractable(i, backwards: true);
            }

            menuItem.navigation = navigation;
        }
    }

    private Selectable GetNextInteractable(int index, bool backwards = false)
    {
        if (index < 0 || index >= _menuItems.Count) return null;

        var step = backwards ? -1 : 1;
        var offset = 0;
        for (var i = 0; i < _menuItems.Count; i++)
        {
            offset += step;
            var nextIndex = (index + offset + _menuItems.Count) % _menuItems.Count;
            var nextItem = _menuItems[nextIndex];
            if (nextItem.interactable)
                return nextItem;
        }

        return null;
    }

    private void OnEnable()
    {
        StartCoroutine(WaitAfterEnable());

        foreach (var rebuildItem in rebuildList)
        {
            LayoutRebuilder.ForceRebuildLayoutImmediate(rebuildItem);
        }

        InputTypeTracker.Instance.OnInputTypeChanged += OnInputTypeChanged;
        
        ActiveMenu = this;
    }

    private void OnDisable()
    {
        RewiredPlayer?.RemoveInputEventDelegate(OnBack);
        
        if (ActiveMenu == this)
            ActiveMenu = null;
    }

    private void OnInputTypeChanged(InputType inputType)
    {
       if (inputType == InputType.Gamepad && EventSystem.current.currentSelectedGameObject == null)
           EventSystem.current.SetSelectedGameObject(defaultSelectedItem);
    }

    private IEnumerator WaitAfterEnable()
    {
        yield return null;

        RewiredPlayer?.AddInputEventDelegate(OnBack, UpdateLoopType.Update, InputActionEventType.ButtonJustPressed, RewiredConsts.Action.UIBack);
        OnEnableLate();
    }

    private void OnEnableLate()
    {
        if (defaultSelectedItem == null)
            return;

        var objectToSelect = _lastSelectedGameObject == null ? defaultSelectedItem : _lastSelectedGameObject;
        EventSystem.current.SetSelectedGameObject(objectToSelect);
    }

    private void OnBack(InputActionEventData obj)
    {
        if (closeWindowOnBack)
        {
            window.Close();
        }
        else
        {
            GoBack();
        }
    }

    public void GoToMenu(Menu nextMenu)
    {
        _lastSelectedGameObject = EventSystem.current.currentSelectedGameObject;
        gameObject.SetActive(false);
        nextMenu.PreviousMenu = this;
        nextMenu.gameObject.SetActive(true);
    }

    public void GoBack()
    {
        if (PreviousMenu == null)
        {
            if (closeWindowOnBack)
                window.Close();

            return;
        }

        gameObject.SetActive(false);
        PreviousMenu.gameObject.SetActive(true);

        _lastSelectedGameObject = null;
    }

    public void OnWindowOpen()
    {
        gameObject.SetActive(isRootMenu);
    }
}